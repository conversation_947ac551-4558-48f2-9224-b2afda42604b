export interface User {
  id: string;
  email: string;
  is_2fa_enabled: boolean;
  tenants: TenantInfo[];
  role?: {
    permissions: Array<{ name: string; description?: string }>;
  };
}

export interface TenantInfo {
  id: string;
  name: string;
  role: string;
  permissions: string[];
}

export interface LoginResponse {
  access_token?: string;
  temp_token?: string;
  token_type: string;
  requires_2fa: boolean;
  message: string;
}

export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

export interface Invoice {
  id: string;
  tenant_id: string;
  import_typ: string;
  metadata_ERP?: any;
  supplier_name?: string;
  invoice_number?: string;
  invoice_date?: string;
  due_date?: string;
  total_amount?: number;
  currency?: string;
  original_filename?: string;
  file_type?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'needs_review';
  processing_error?: string;
  created_at: string;
  updated_at: string;
  accounting_entries?: AccountingEntry[];
}

export interface AccountingEntry {
  id: string;
  account_code: string;
  account_name: string;
  debit_amount?: number;
  credit_amount?: number;
  description?: string;
  confidence_score: number;
  is_validated: boolean;
}

export interface ActionItem {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'review' | 'validation' | 'error' | 'manual_entry';
  is_completed: boolean;
  created_at: string;
  invoice_id?: string;
  assigned_user?: {
    id: string;
    email: string;
  };
  invoice?: {
    id: string;
    supplier_name: string;
    status: string;
  };
}

export interface TwoFASetupResponse {
  secret: string;
  qr_code: string;
  backup_codes: string[];
}

export interface ApiError {
  detail: string;
}

// Integration types
export type IntegrationType = 'FORTNOX' | 'VISMA' | 'HTTP';

export interface IntegrationConfigField {
  name: string;
  type: 'string' | 'url' | 'select';
  required: boolean;
  description: string;
  options?: string[];
  sensitive?: boolean;
}

export interface AvailableIntegration {
  type: IntegrationType;
  name: string;
  description: string;
  oauth_required: boolean;
  config_fields: IntegrationConfigField[];
}

export interface InvoiceIntegration {
  id: string;
  tenant_id: string;
  integration_type: IntegrationType;
  name?: string;
  description?: string;
  is_active: boolean;
  last_sync_at?: string;
  last_sync_status?: 'success' | 'failed' | 'partial';
  last_error?: string;
  created_at: string;
  updated_at: string;
}

export interface InvoiceIntegrationCreate {
  integration_type: IntegrationType;
  configuration: Record<string, any>;
  name?: string;
  description?: string;
  is_active?: boolean;
}

export interface InvoiceIntegrationUpdate {
  configuration?: Record<string, any>;
  name?: string;
  description?: string;
  is_active?: boolean;
}

export interface IntegrationSyncResult {
  integration_id: string;
  integration_type: IntegrationType;
  success: boolean;
  invoices_count: number;
  message: string;
  error_details?: Record<string, any>;
  synced_at?: string;
}

export interface ManualSyncResponse {
  success: boolean;
  message: string;
  task_id: string;
  tenant_id: string;
}

export interface ScheduleSettings {
  enabled: boolean;
  cron_expression: string;
  last_run?: string;
  next_run?: string;
}

export interface ScheduleSettingsUpdate {
  enabled?: boolean;
  cron_expression?: string;
}

export interface ConnectionTestResult {
  success: boolean;
  message: string;
  details?: Record<string, any>;
  tested_at?: string;
}

// New invoice processing types
export type ImportType = 'manuell' | 'Visma eEkonomi' | 'Fortnox';
export type SessionStatus = 'pending' | 'processing' | 'completed' | 'failed';
export type ProcessingStep = 'extrahera' | 'kontext' | 'hitta_konto' | 'bokfora';

export interface ProcessingSession {
  id: string;
  invoice_id: string;
  status: SessionStatus;
  current_step?: ProcessingStep;
  created_at: string;
  updated_at: string;
  error_message?: string;
  failed_step?: string;
}

export interface SessionLog {
  id: string;
  step_name: ProcessingStep;
  prompt_sent: string;
  llm_response: string;
  reasoning?: string;
  execution_time_ms?: number;
  success: boolean;
  error_message?: string;
  input_tokens?: number;
  output_tokens?: number;
  created_at: string;
}

export interface ProcessingResults {
  extracted_data?: string;
  extracted_reasoning?: string;
  context_data?: string;
  context_reasoning?: string;
  account_data?: any;
  account_reasoning?: string;
  booking_result?: any;
  booking_reasoning?: string;
}

export interface SessionStatistics {
  total_steps: number;
  successful_steps: number;
  failed_steps: number;
  total_execution_time_ms: number;
  average_execution_time_ms: number;
  steps_completed: string[];
  total_input_tokens: number;
  total_output_tokens: number;
  total_tokens: number;
}

export interface SessionDetail {
  session: ProcessingSession;
  invoice?: Invoice;
  processing_results: ProcessingResults;
  logs: SessionLog[];
  statistics: SessionStatistics;
}

export interface SessionSummaryItem {
  id: string;
  invoice_id: string;
  status: SessionStatus;
  current_step?: ProcessingStep;
  created_at: string;
  updated_at: string;
  error_message?: string;
  failed_step?: string;
  invoice?: {
    supplier_name?: string;
    original_filename?: string;
    import_typ?: string;
  };
  log_count: number;
}

export interface TenantStatistics {
  sessions_by_status: Record<string, number>;
  total_sessions: number;
  total_logs: number;
  successful_logs: number;
  failed_logs: number;
  success_rate: number;
}

export interface SessionsSummary {
  sessions: SessionSummaryItem[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    has_more: boolean;
  };
  statistics: TenantStatistics;
}

export interface ProcessingTaskResponse {
  task_id: string;
  session_id: string;
  status: string;
  message: string;
}

export interface LLMProviderInfo {
  provider: string;
  status: string;
}



// Utility function to combine CSS class names
export function classNames(...classes: string[]): string {
  return classes.filter(Boolean).join(' ');
}
