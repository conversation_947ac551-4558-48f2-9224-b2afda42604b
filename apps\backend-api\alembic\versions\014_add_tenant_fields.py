"""Add company_context and tenant_type fields to tenants table

Revision ID: 014_add_tenant_fields
Revises: 013_add_token_tracking
Create Date: 2025-01-08 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '014_add_tenant_fields'
down_revision = '013_add_token_tracking'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create enum type for tenant_type
    tenant_type_enum = sa.Enum('standard', 'agency', name='tenant_type_enum')
    tenant_type_enum.create(op.get_bind())
    
    # Add company_context field (Text, nullable)
    op.add_column('tenants', sa.Column('company_context', sa.Text(), nullable=True))
    
    # Add tenant_type field (Enum, default 'standard')
    op.add_column('tenants', sa.Column('tenant_type', tenant_type_enum, nullable=False, server_default='standard'))


def downgrade() -> None:
    # Remove the columns
    op.drop_column('tenants', 'tenant_type')
    op.drop_column('tenants', 'company_context')
    
    # Drop the enum type
    tenant_type_enum = sa.Enum('standard', 'agency', name='tenant_type_enum')
    tenant_type_enum.drop(op.get_bind())
