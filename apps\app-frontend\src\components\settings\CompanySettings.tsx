import React, { useState } from 'react';
import { BuildingOfficeIcon } from '@heroicons/react/24/outline';

export function CompanySettings() {
  const [companyInfo, setCompanyInfo] = useState('');

  const handleCompanyInfoChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCompanyInfo(e.target.value);
  };

  return (
    <div className="space-y-6">
      <div className="card-container">
        <h3 className="section-title flex items-center">
          <BuildingOfficeIcon className="h-6 w-6 mr-2 text-indigo-600" />
          Company Information
        </h3>
        <div className="bg-gradient-to-r from-purple-50 via-indigo-50 to-pink-50 rounded-2xl p-6">
          <label htmlFor="companyInfo" className="block text-sm font-medium text-gray-700 mb-2">
            About Your Company
          </label>
          <textarea
            id="companyInfo"
            value={companyInfo}
            onChange={handleCompanyInfoChange}
            rows={10}
            className="input-custom w-full"
            placeholder="Enter information about your company, its mission, and other relevant details..."
          />
        </div>
      </div>
    </div>
  );
}