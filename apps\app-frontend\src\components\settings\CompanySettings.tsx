import React, { useState, useEffect } from 'react';
import { BuildingOfficeIcon } from '@heroicons/react/24/outline';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-hot-toast';
import { tenantApi } from '../../services/api';
import { TenantType } from '../../types';

export function CompanySettings() {
  const [companyInfo, setCompanyInfo] = useState('');
  const [tenantType, setTenantType] = useState<TenantType>(TenantType.STANDARD);
  const queryClient = useQueryClient();

  // Fetch tenant data
  const { data: tenant, isLoading, error } = useQuery('tenant', tenantApi.getTenant);

  // Update tenant mutation
  const updateTenantMutation = useMutation(tenantApi.updateTenant, {
    onSuccess: () => {
      queryClient.invalidateQueries('tenant');
      toast.success('Company settings updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Failed to update company settings');
    }
  });

  // Update local state when tenant data is loaded
  useEffect(() => {
    if (tenant) {
      setCompanyInfo(tenant.company_context || '');
      setTenantType(tenant.tenant_type);
    }
  }, [tenant]);

  const handleCompanyInfoChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCompanyInfo(e.target.value);
  };

  const handleTenantTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setTenantType(e.target.value as TenantType);
  };

  const handleSave = () => {
    updateTenantMutation.mutate({
      company_context: companyInfo,
      tenant_type: tenantType
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="card-container">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="card-container">
          <div className="text-red-600">Failed to load company settings</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="card-container">
        <h3 className="section-title flex items-center">
          <BuildingOfficeIcon className="h-6 w-6 mr-2 text-indigo-600" />
          Company Information
        </h3>
        <div className="bg-gradient-to-r from-purple-50 via-indigo-50 to-pink-50 rounded-2xl p-6 space-y-6">
          {/* Tenant Type Selection */}
          <div>
            <label htmlFor="tenantType" className="block text-sm font-medium text-gray-700 mb-2">
              Organization Type
            </label>
            <select
              id="tenantType"
              value={tenantType}
              onChange={handleTenantTypeChange}
              className="input-custom w-full"
            >
              <option value={TenantType.STANDARD}>Standard Organization</option>
              <option value={TenantType.AGENCY}>Agency</option>
            </select>
          </div>

          {/* Company Context */}
          <div>
            <label htmlFor="companyInfo" className="block text-sm font-medium text-gray-700 mb-2">
              About Your Company
            </label>
            <textarea
              id="companyInfo"
              value={companyInfo}
              onChange={handleCompanyInfoChange}
              onBlur={handleSave}
              rows={10}
              className="input-custom w-full"
              placeholder="Enter information about your company, its mission, and other relevant details..."
            />
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              onClick={handleSave}
              disabled={updateTenantMutation.isLoading}
              className="btn-primary"
            >
              {updateTenantMutation.isLoading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}